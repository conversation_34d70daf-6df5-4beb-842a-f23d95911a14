"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\shared_preferences_android\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1895f37538769184138471aa9d142e81\\transformed\\jetified-flutter_embedding_debug-1.0.0-dd93de6fb1776398bf586cbd477deade1391c7e4.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\61878dd502914430b391d8ce1fb24a1d\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a98bc5cb1ab42c419e56770f5f05e0b3\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7e40a799addf4ca9f76b2995e3a0691b\\transformed\\jetified-fragment-ktx-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3f1267c71036d0326b2fcd03b41f6af6\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f263ec66b166d11a3a5ae909890049d\\transformed\\recyclerview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\925dd24c97b22bef8440d74c27e2ba15\\transformed\\jetified-activity-ktx-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\257689b9e289d9fcaf9af50d006d8592\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65eb143445a755550d6684982d05de5\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\37000a248578edfe09b6db0cb8f9438a\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bfd6d67b58f0cc9fa82eda9c7e1e484\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\beb193cb82804284c6b21e42f8795c56\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5d0e16eb1cc0cb3348e2bff76fd4f6f6\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c9c20c261d079dcd21466ea55284dc41\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ac422a55800c5ddda26c4d99128e45b\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ddde63fe7de48b2407eddff2ec79750f\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5797d04c6260f349780d920384e4cca\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\474ddb7ca4c2dbb62b6212e32c3329ca\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\15b4ebde6f04e999dc059d0ababb2650\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\046e4aeccd8a37cb3fa426b126c2dfbf\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3e78585dfe99d4b4deb595cbe1351e9e\\transformed\\jetified-appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a3d799c2b4bb7f2578cfc8fbaeccdb79\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3ac11a3b94ff79ca2c786d224d032729\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\28f5009f8f226b1e6a2565b757836e84\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b0ea8c49b931400dc2d0750bbcafdf0e\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0347220b53d623cba17deda76fe6c465\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\81ccf810fd82a7d643799e685479bf32\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\28cf187e42e320c84982704eec0121dc\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\537a9be0242cfe1ace4bb91202fc1b91\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c56945fedc41249edd62011c139e6b4b\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2841b72e21daf667f3924e723ece2cc\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a52bbaba842cae918f06b7749e359638\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\faacab26541f69acfddc83853fea17cd\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b18d3259e875007fc67449799814b67d\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c03a11240a6bea3a3cca563e32d57997\\transformed\\jetified-datastore-preferences-core-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17e4ca9c31f2c66ae25bdec823b744c8\\transformed\\jetified-datastore-core-okio-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\66a241b2a6239f8d463da6ae6920f274\\transformed\\jetified-datastore-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\718fafa707d04aeb9ab37edac00ab879\\transformed\\jetified-datastore-preferences-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae7fdb81945233b1808449ef4013b815\\transformed\\jetified-datastore-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\925852cf20748ca850745674667c43dd\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\635f7924f5647b84d47712daee7a50aa\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4f81d61720b8fde7aa2054b6176caa67\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a56e3b243a9b2fe81f18a812ffc3d5e1\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aea47234a34e149c627ab12ed6c50a6d\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8f4a4ebba09d790033a6b1e1053ca65a\\transformed\\jetified-collection-ktx-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45663761ea0e8b52695432bba1bfe07b\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ec078fcc7bbf76fb155e58fe177f757d\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\95eae97255755fdc1cbae2b5cbdfff5b\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6505bbcfafddd33df341a6903339e636\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\88421ed317c6b4564b23d87f2c3585e4\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57b37ef1c93f65ea58bd061b346ac064\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ac2bf21fde5cace55efe5d36ee310b5\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\772a4a5220f99a6731c3fc33a80435e7\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c4db124bad8e869299914c80fa949ed9\\transformed\\jetified-okio-jvm-3.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80f10a1c906e02c65164a3c7deb992d6\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d9d91db22880e405121dfb04f78f6647\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fc1cc6c69c54f418600f43501f6e26e\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f834d7598239241c611afe9501c423d9\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ea7a2539d13ad95956d64c625bc74862\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0659534bebb2368eaaa525d47bc017e6\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\40f8fd6d7ee7b904e6278b38bd7dfb22\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\shared_preferences_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "shared_preferences_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\LegacySharedPreferencesPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\Messages.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesListEncoder.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\MessagesAsync.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\StringListObjectInputStream.kt"