com.example.dalti_provider.app-jetified-camera-camera2-1.3.3-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071211c76e4316751f645a33b3500b32\transformed\jetified-camera-camera2-1.3.3\res
com.example.dalti_provider.app-fragment-1.7.1-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10e13d6e991a6009d2f8a325c8784abb\transformed\fragment-1.7.1\res
com.example.dalti_provider.app-jetified-window-java-1.2.0-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\11bdc990f9dc4126be5c5c42c8fda3cf\transformed\jetified-window-java-1.2.0\res
com.example.dalti_provider.app-jetified-activity-1.8.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\142ea7f7f061cb61664f0b0bbc488bec\transformed\jetified-activity-1.8.1\res
com.example.dalti_provider.app-jetified-core-1.0.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a614e682174a01eb2d6703f65a41cb\transformed\jetified-core-1.0.0\res
com.example.dalti_provider.app-coordinatorlayout-1.0.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c0260772a8f78ccf77159c3afaef423\transformed\coordinatorlayout-1.0.0\res
com.example.dalti_provider.app-jetified-lifecycle-runtime-ktx-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\27a77d6341253ed9503f46580f35588c\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.dalti_provider.app-jetified-core-ktx-1.13.1-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35da6644dcf9707ae1c734ff72490f20\transformed\jetified-core-ktx-1.13.1\res
com.example.dalti_provider.app-jetified-camera-core-1.3.3-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3dabbe3b9b23d8f3cc325a0e3112d665\transformed\jetified-camera-core-1.3.3\res
com.example.dalti_provider.app-localbroadcastmanager-1.1.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3de49e3d59874a6a98ce23843c89b5f4\transformed\localbroadcastmanager-1.1.0\res
com.example.dalti_provider.app-jetified-annotation-experimental-1.4.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e588f8ba125aee2a931b762947a8976\transformed\jetified-annotation-experimental-1.4.0\res
com.example.dalti_provider.app-jetified-startup-runtime-1.1.1-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\471946729bdc472e1496f0d8321afef6\transformed\jetified-startup-runtime-1.1.1\res
com.example.dalti_provider.app-lifecycle-livedata-core-2.7.0-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a35b18d5f7bb60a0da954f1a144e548\transformed\lifecycle-livedata-core-2.7.0\res
com.example.dalti_provider.app-jetified-fragment-ktx-1.7.1-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a88f7481e56743c8ee7fce9ad0b2ca1\transformed\jetified-fragment-ktx-1.7.1\res
com.example.dalti_provider.app-jetified-camera-lifecycle-1.3.3-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c96c4495c72874788e80114b1226a15\transformed\jetified-camera-lifecycle-1.3.3\res
com.example.dalti_provider.app-jetified-ads-adservices-java-1.1.0-beta11-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d022b9f3e6d11994777a4a74587c1da\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.dalti_provider.app-jetified-firebase-common-21.0.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\res
com.example.dalti_provider.app-jetified-lifecycle-viewmodel-ktx-2.7.0-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e3ef69fa2c919df09145f864874ff43\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.dalti_provider.app-browser-1.8.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e526f7e181b418465f348b80eefa1d1\transformed\browser-1.8.0\res
com.example.dalti_provider.app-core-1.13.1-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\res
com.example.dalti_provider.app-jetified-datastore-core-release-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73fe36232fc5415bf453d7c74d5093e2\transformed\jetified-datastore-core-release\res
com.example.dalti_provider.app-jetified-window-1.2.0-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\res
com.example.dalti_provider.app-preference-1.2.1-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92f9729480504779d6e2d65465688ed0\transformed\preference-1.2.1\res
com.example.dalti_provider.app-transition-1.4.1-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95a2390c3a8095413944d1184a09e1b7\transformed\transition-1.4.1\res
com.example.dalti_provider.app-jetified-lifecycle-process-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\res
com.example.dalti_provider.app-recyclerview-1.0.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d02d2116abcce1bddd944745c3bbc49\transformed\recyclerview-1.0.0\res
com.example.dalti_provider.app-core-runtime-2.2.0-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a020228c06633d6f75b95a8b2267f97c\transformed\core-runtime-2.2.0\res
com.example.dalti_provider.app-jetified-ads-adservices-1.1.0-beta11-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08ad2e43d7092c689ba72edfa0286f7\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.dalti_provider.app-jetified-lifecycle-livedata-core-ktx-2.7.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a62b7e9c82af4c7c33862429c597f9e6\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.dalti_provider.app-jetified-firebase-messaging-24.1.2-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\res
com.example.dalti_provider.app-jetified-profileinstaller-1.3.1-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\res
com.example.dalti_provider.app-slidingpanelayout-1.2.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3c0f13f5ae4342307cacd818cd2465d\transformed\slidingpanelayout-1.2.0\res
com.example.dalti_provider.app-jetified-datastore-preferences-release-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5eeaefa1607ccc6937108bd56fdae16\transformed\jetified-datastore-preferences-release\res
com.example.dalti_provider.app-jetified-security-crypto-1.1.0-alpha06-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b6701e4b78404f600bb56dcfd2cac21e\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.example.dalti_provider.app-jetified-tracing-1.2.0-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bce69d20c20c5e24bf497bed3474cd16\transformed\jetified-tracing-1.2.0\res
com.example.dalti_provider.app-jetified-savedstate-ktx-1.2.1-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cb76db706bf06bcd9c09ab90a051e7\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.dalti_provider.app-appcompat-1.1.0-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9bce956c4646bab511e3d88137b3b51\transformed\appcompat-1.1.0\res
com.example.dalti_provider.app-jetified-play-services-base-18.5.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd830b9d4bbdcad3f13d1c376e8bfba0\transformed\jetified-play-services-base-18.5.0\res
com.example.dalti_provider.app-media-1.1.0-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce6235c309605785a1fbf1a2cbb757b5\transformed\media-1.1.0\res
com.example.dalti_provider.app-lifecycle-viewmodel-2.7.0-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d5094bd70fd387ac8c10771d57bc9d42\transformed\lifecycle-viewmodel-2.7.0\res
com.example.dalti_provider.app-jetified-appcompat-resources-1.1.0-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d7486fe3032ca86f6b13576ae0ef5a\transformed\jetified-appcompat-resources-1.1.0\res
com.example.dalti_provider.app-jetified-play-services-basement-18.5.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e829c71dd9346aa9726de2f25cdf8480\transformed\jetified-play-services-basement-18.5.0\res
com.example.dalti_provider.app-jetified-datastore-release-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1a16349fd9e21a0c1c7285c9f99f104\transformed\jetified-datastore-release\res
com.example.dalti_provider.app-jetified-activity-ktx-1.8.1-43 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f736b0922961b240a742bde57b2ddd4d\transformed\jetified-activity-ktx-1.8.1\res
com.example.dalti_provider.app-lifecycle-runtime-2.7.0-44 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbc515739f57102549ddaf76cec827be\transformed\lifecycle-runtime-2.7.0\res
com.example.dalti_provider.app-jetified-savedstate-1.2.1-45 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fddfdf4336f6a41c8a1d413b57534960\transformed\jetified-savedstate-1.2.1\res
com.example.dalti_provider.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-46 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff02f656c17a07acc1f913d5411a1d2f\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.dalti_provider.app-lifecycle-livedata-2.7.0-47 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff90a907e4c29e229627ca2d53fac994\transformed\lifecycle-livedata-2.7.0\res
com.example.dalti_provider.app-debug-48 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\debug\res
com.example.dalti_provider.app-main-49 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\res
com.example.dalti_provider.app-pngs-50 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\pngs\debug
com.example.dalti_provider.app-res-51 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\processDebugGoogleServices
com.example.dalti_provider.app-resValues-52 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\generated\res\resValues\debug
com.example.dalti_provider.app-packageDebugResources-53 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.dalti_provider.app-packageDebugResources-54 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.dalti_provider.app-debug-55 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.dalti_provider.app-debug-56 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-57 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-58 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-59 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\flutter_local_notifications\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-60 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-61 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\flutter_secure_storage\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-62 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-63 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\mobile_scanner\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-64 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-65 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.dalti_provider.app-debug-66 D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
