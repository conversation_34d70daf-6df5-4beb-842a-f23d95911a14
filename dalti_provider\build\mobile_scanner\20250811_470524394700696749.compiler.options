"-Xallow-no-source-files" "-classpath" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\mobile_scanner\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1895f37538769184138471aa9d142e81\\transformed\\jetified-flutter_embedding_debug-1.0.0-dd93de6fb1776398bf586cbd477deade1391c7e4.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\443accbb0c47f1d858c9c986fd58b4ff\\transformed\\jetified-camera-core-1.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9844ca111b49a76a0bd7950144ba8802\\transformed\\jetified-camera-camera2-1.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\15d50ea14d3e6917f09b27a716215734\\transformed\\jetified-camera-lifecycle-1.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4736f0deaacd8bf8e51684feffbac0ce\\transformed\\jetified-barcode-scanning-17.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\997a1a919a9ddeabae116c055332ecbc\\transformed\\jetified-play-services-mlkit-barcode-scanning-18.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3e4ef569cadf708d3e5f5e761995b53b\\transformed\\jetified-barcode-scanning-common-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5a3c942b9ba8751cb2e58fcf0d89f765\\transformed\\jetified-vision-common-17.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7702f9732269b052c470a9a0dd4cd99d\\transformed\\jetified-common-18.9.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\74c304d2c3f324e847ff68d94dc6cf71\\transformed\\jetified-play-services-base-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f720992e33c306fc685a1535fd2cd5b\\transformed\\jetified-vision-interfaces-16.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\91a1f502c7b60b7df1a9d11c3c44abe2\\transformed\\jetified-play-services-tasks-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fe418bedd8efd005cf180d7db7e846ec\\transformed\\jetified-play-services-basement-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3f1267c71036d0326b2fcd03b41f6af6\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\257689b9e289d9fcaf9af50d006d8592\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7bfd6d67b58f0cc9fa82eda9c7e1e484\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5d0e16eb1cc0cb3348e2bff76fd4f6f6\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\beb193cb82804284c6b21e42f8795c56\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ddde63fe7de48b2407eddff2ec79750f\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5797d04c6260f349780d920384e4cca\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\474ddb7ca4c2dbb62b6212e32c3329ca\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\15b4ebde6f04e999dc059d0ababb2650\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\046e4aeccd8a37cb3fa426b126c2dfbf\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\647fa3b7210d0a46cc4bfe633878af3c\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c56945fedc41249edd62011c139e6b4b\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c2841b72e21daf667f3924e723ece2cc\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a52bbaba842cae918f06b7749e359638\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\faacab26541f69acfddc83853fea17cd\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b18d3259e875007fc67449799814b67d\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\925852cf20748ca850745674667c43dd\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4f81d61720b8fde7aa2054b6176caa67\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a56e3b243a9b2fe81f18a812ffc3d5e1\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aea47234a34e149c627ab12ed6c50a6d\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1fe44932daf7e0a2f1d51651b1fae1d8\\transformed\\jetified-transport-backend-cct-2.3.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf06a5d5d9aa0e54c2b8a739f5de85ff\\transformed\\jetified-transport-runtime-2.2.6-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e9ff29383920f2984d346cdbdeb315c6\\transformed\\jetified-transport-api-2.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3d806030da40b0c64ed4fa623618ffe2\\transformed\\jetified-firebase-components-16.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ace5ba179804834846ad369ccbc00f7\\transformed\\jetified-firebase-encoders-json-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6a57d0bc3a3ff2ffa67af28ed0d4a02c\\transformed\\jetified-firebase-encoders-16.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34296b36bea7a2ebc6e0af42b0e230c6\\transformed\\exifinterface-1.3.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\57b37ef1c93f65ea58bd061b346ac064\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c7b3332534b6681267df03d0cb19cca3\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\51f94b77bdac3d5377959d00d319de42\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\297351c9927ceb418c5885cbbdc38120\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d3811e73ff159932bee6b998ed17bf66\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2fc1cc6c69c54f418600f43501f6e26e\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f834d7598239241c611afe9501c423d9\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\96e44878f61bedb00aad7caf4d11e51e\\transformed\\jetified-listenablefuture-1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ea7a2539d13ad95956d64c625bc74862\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0659534bebb2368eaaa525d47bc017e6\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\40f8fd6d7ee7b904e6278b38bd7dfb22\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aeccfbac768ca651c3d7b0d4bc12c3f4\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2dda55058c893eb9d4cc8ea4260aa22b\\transformed\\jetified-image-1.0.0-beta1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\945ca9103e6437ff4882545c0ccabb89\\transformed\\jetified-firebase-annotations-16.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-34\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\DALTI-APP-DEV\\dalti-provider-flutter\\dalti_provider\\build\\mobile_scanner\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "mobile_scanner_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\BarcodeHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScanner.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerCallbacks.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerExceptions.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerHandler.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerPermissions.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerPermissionsListener.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\MobileScannerUtilities.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\BarcodeFormats.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\DetectionSpeed.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\objects\\MobileScannerStartParameters.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\utils\\Yuv.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mobile_scanner-5.2.3\\android\\src\\main\\kotlin\\dev\\steenbakker\\mobile_scanner\\utils\\YuvToRgbConverter.kt"