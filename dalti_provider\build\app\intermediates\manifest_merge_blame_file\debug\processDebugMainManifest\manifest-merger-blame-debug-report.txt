1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.dalti_provider"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:2:5-66
15-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:2:22-64
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:3:5-67
16-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:3:22-65
17    <!--
18 Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:58:5-63:15
25        <intent>
25-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:59:9-62:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:60:13-72
26-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:60:21-70
27
28            <data android:mimeType="text/plain" />
28-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:61:13-50
28-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:61:19-48
29        </intent>
30        <intent>
30-->[:file_picker] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
31            <action android:name="android.intent.action.GET_CONTENT" />
31-->[:file_picker] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
31-->[:file_picker] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
32
33            <data android:mimeType="*/*" />
33-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:61:13-50
33-->D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\android\app\src\main\AndroidManifest.xml:61:19-48
34        </intent>
35    </queries>
36
37    <uses-permission android:name="android.permission.CAMERA" />
37-->[:mobile_scanner] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\mobile_scanner\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
37-->[:mobile_scanner] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\mobile_scanner\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-62
38
39    <uses-feature
39-->[:mobile_scanner] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\mobile_scanner\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-11:36
40        android:name="android.hardware.camera"
40-->[:mobile_scanner] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\mobile_scanner\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-47
41        android:required="false" />
41-->[:mobile_scanner] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\mobile_scanner\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-33
42
43    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
43-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
43-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
44    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
44-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
44-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
45    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
45-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
45-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
46    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
46-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
46-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
47    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
47-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
47-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
48    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
48-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
49    <uses-permission android:name="android.permission.VIBRATE" />
49-->[:flutter_local_notifications] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
49-->[:flutter_local_notifications] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
50    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
50-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
50-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
51
52    <permission
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
53        android:name="com.example.dalti_provider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.example.dalti_provider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
57
58    <application
59        android:name="android.app.Application"
60        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70f5dcd185a85b682509b51ca6edee27\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
61        android:debuggable="true"
62        android:extractNativeLibs="true"
63        android:icon="@mipmap/ic_launcher"
64        android:label="dalti_provider" >
65        <activity
66            android:name="com.example.dalti_provider.MainActivity"
67            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
68            android:exported="true"
69            android:hardwareAccelerated="true"
70            android:launchMode="singleTop"
71            android:taskAffinity=""
72            android:theme="@style/LaunchTheme"
73            android:windowSoftInputMode="adjustResize" >
74
75            <!--
76                 Specifies an Android theme to apply to this Activity as soon as
77                 the Android process has started. This theme is visible to the user
78                 while the Flutter UI initializes. After that, this theme continues
79                 to determine the Window background behind the Flutter UI.
80            -->
81            <meta-data
82                android:name="io.flutter.embedding.android.NormalTheme"
83                android:resource="@style/NormalTheme" />
84
85            <intent-filter>
86                <action android:name="android.intent.action.MAIN" />
87
88                <category android:name="android.intent.category.LAUNCHER" />
89            </intent-filter>
90        </activity>
91
92        <service
93            android:name="com.google.firebase.messaging.FirebaseMessagingService"
94            android:directBootAware="true"
94-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
95            android:exported="false" >
96            <intent-filter>
96-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
97                <action android:name="com.google.firebase.MESSAGING_EVENT" />
97-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
97-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
98            </intent-filter>
99            <intent-filter android:priority="-500" >
99-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
100                <action android:name="com.google.firebase.MESSAGING_EVENT" />
100-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
100-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
101            </intent-filter>
102        </service>
103        <!-- FCM Notification Configuration -->
104        <meta-data
105            android:name="com.google.firebase.messaging.default_notification_icon"
106            android:resource="@mipmap/ic_launcher" />
107        <meta-data
108            android:name="com.google.firebase.messaging.default_notification_color"
109            android:resource="@android:color/holo_blue_light" />
110        <meta-data
111            android:name="com.google.firebase.messaging.default_notification_channel_id"
112            android:value="default_channel_id" />
113        <!--
114             Don't delete the meta-data below.
115             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
116        -->
117        <meta-data
118            android:name="flutterEmbedding"
119            android:value="2" />
120
121        <service
121-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
122            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
122-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
123            android:exported="false"
123-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
124            android:permission="android.permission.BIND_JOB_SERVICE" />
124-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
125        <service
125-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
126            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
126-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
127            android:exported="false" >
127-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
128            <intent-filter>
128-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
129                <action android:name="com.google.firebase.MESSAGING_EVENT" />
129-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
129-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
130            </intent-filter>
131        </service>
132
133        <receiver
133-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
134            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
134-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
135            android:exported="true"
135-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
136            android:permission="com.google.android.c2dm.permission.SEND" >
136-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
137            <intent-filter>
137-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
138                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
138-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
138-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
139            </intent-filter>
140        </receiver>
141
142        <service
142-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
143            android:name="com.google.firebase.components.ComponentDiscoveryService"
143-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
144            android:directBootAware="true"
144-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
145            android:exported="false" >
145-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:56:13-37
146            <meta-data
146-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
147                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
147-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
149            <meta-data
149-->[:firebase_core] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
150                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
150-->[:firebase_core] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[:firebase_core] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
152            <meta-data
152-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
153                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
153-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
155            <meta-data
155-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
156                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
156-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
158            <meta-data
158-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
159                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
159-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ef1d47f53df30555e16cb05c41949c2\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
161            <meta-data
161-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3d7e75892408be13b45c807ac60403\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
162                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
162-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3d7e75892408be13b45c807ac60403\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3d7e75892408be13b45c807ac60403\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
164            <meta-data
164-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3d7e75892408be13b45c807ac60403\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
165                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
165-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3d7e75892408be13b45c807ac60403\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb3d7e75892408be13b45c807ac60403\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
167            <meta-data
167-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e86cabd7af73744b88e287e95fba39\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
168                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
168-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e86cabd7af73744b88e287e95fba39\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18e86cabd7af73744b88e287e95fba39\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
170            <meta-data
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
171                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
173            <meta-data
173-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32065e04cfea550871c713a370458132\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
174                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
174-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32065e04cfea550871c713a370458132\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32065e04cfea550871c713a370458132\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
176        </service>
177
178        <provider
178-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
179            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
179-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
180            android:authorities="com.example.dalti_provider.flutterfirebasemessaginginitprovider"
180-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
181            android:exported="false"
181-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
182            android:initOrder="99" />
182-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
183
184        <receiver
184-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
185            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
185-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
186            android:exported="true"
186-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
187            android:permission="com.google.android.c2dm.permission.SEND" >
187-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
188            <intent-filter>
188-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
189                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
189-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
189-->[:firebase_messaging] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
190            </intent-filter>
191
192            <meta-data
192-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
193                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
193-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
194                android:value="true" />
194-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a6b0e05e12f9cc70d923c870a0221425\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
195        </receiver>
196
197        <provider
197-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
198            android:name="com.google.firebase.provider.FirebaseInitProvider"
198-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
199            android:authorities="com.example.dalti_provider.firebaseinitprovider"
199-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
200            android:directBootAware="true"
200-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
201            android:exported="false"
201-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
202            android:initOrder="100" />
202-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60696d9f888953ab17cdf14174ff7197\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
203
204        <service
204-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
205            android:name="com.baseflow.geolocator.GeolocatorLocationService"
205-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
206            android:enabled="true"
206-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
207            android:exported="false"
207-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
208            android:foregroundServiceType="location" />
208-->[:geolocator_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
209
210        <activity
210-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
211            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
211-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
212            android:exported="false"
212-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
213            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
213-->[:url_launcher_android] D:\DALTI-APP-DEV\dalti-provider-flutter\dalti_provider\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
214        <!--
215        Service for holding metadata. Cannot be instantiated.
216        Metadata will be merged from other manifests.
217        -->
218        <service
218-->[androidx.camera:camera-core:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3dabbe3b9b23d8f3cc325a0e3112d665\transformed\jetified-camera-core-1.3.3\AndroidManifest.xml:29:9-33:78
219            android:name="androidx.camera.core.impl.MetadataHolderService"
219-->[androidx.camera:camera-core:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3dabbe3b9b23d8f3cc325a0e3112d665\transformed\jetified-camera-core-1.3.3\AndroidManifest.xml:30:13-75
220            android:enabled="false"
220-->[androidx.camera:camera-core:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3dabbe3b9b23d8f3cc325a0e3112d665\transformed\jetified-camera-core-1.3.3\AndroidManifest.xml:31:13-36
221            android:exported="false" >
221-->[androidx.camera:camera-core:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3dabbe3b9b23d8f3cc325a0e3112d665\transformed\jetified-camera-core-1.3.3\AndroidManifest.xml:32:13-37
222            <meta-data
222-->[androidx.camera:camera-camera2:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071211c76e4316751f645a33b3500b32\transformed\jetified-camera-camera2-1.3.3\AndroidManifest.xml:30:13-32:89
223                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
223-->[androidx.camera:camera-camera2:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071211c76e4316751f645a33b3500b32\transformed\jetified-camera-camera2-1.3.3\AndroidManifest.xml:31:17-103
224                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
224-->[androidx.camera:camera-camera2:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\071211c76e4316751f645a33b3500b32\transformed\jetified-camera-camera2-1.3.3\AndroidManifest.xml:32:17-86
225        </service>
226
227        <uses-library
227-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
228            android:name="androidx.window.extensions"
228-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
229            android:required="false" />
229-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
230        <uses-library
230-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
231            android:name="androidx.window.sidecar"
231-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
232            android:required="false" />
232-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86a6030b488ea3bfb6c0e31f395878f1\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
233
234        <service
234-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20cbba57816a41567e4e6268984f9d9\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
235            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
235-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20cbba57816a41567e4e6268984f9d9\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
236            android:directBootAware="true"
236-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:17:13-43
237            android:exported="false" >
237-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20cbba57816a41567e4e6268984f9d9\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
238            <meta-data
238-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20cbba57816a41567e4e6268984f9d9\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
239                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
239-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20cbba57816a41567e4e6268984f9d9\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e20cbba57816a41567e4e6268984f9d9\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
241            <meta-data
241-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795c71c02622917d12e77719ae928d07\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
242                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
242-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795c71c02622917d12e77719ae928d07\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\795c71c02622917d12e77719ae928d07\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
244            <meta-data
244-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:20:13-22:85
245                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
245-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:21:17-120
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:22:17-82
247        </service>
248
249        <provider
249-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:9:9-13:38
250            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
250-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:10:13-78
251            android:authorities="com.example.dalti_provider.mlkitinitprovider"
251-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:11:13-69
252            android:exported="false"
252-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:12:13-37
253            android:initOrder="99" />
253-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\88fd4ed5fa7407ebadf9b202fec87bec\transformed\jetified-common-18.9.0\AndroidManifest.xml:13:13-35
254
255        <receiver
255-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
256            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
256-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
257            android:enabled="true"
257-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
258            android:exported="false" >
258-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
259        </receiver>
260
261        <service
261-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
262            android:name="com.google.android.gms.measurement.AppMeasurementService"
262-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
263            android:enabled="true"
263-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
264            android:exported="false" />
264-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
265        <service
265-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
266            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
266-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
267            android:enabled="true"
267-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
268            android:exported="false"
268-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
269            android:permission="android.permission.BIND_JOB_SERVICE" />
269-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b95b69d005ac062d71399fdfe2824d66\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
270
271        <activity
271-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd830b9d4bbdcad3f13d1c376e8bfba0\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
272            android:name="com.google.android.gms.common.api.GoogleApiActivity"
272-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd830b9d4bbdcad3f13d1c376e8bfba0\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
273            android:exported="false"
273-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd830b9d4bbdcad3f13d1c376e8bfba0\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
274            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
274-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd830b9d4bbdcad3f13d1c376e8bfba0\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
275
276        <provider
276-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
277            android:name="androidx.startup.InitializationProvider"
277-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
278            android:authorities="com.example.dalti_provider.androidx-startup"
278-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
279            android:exported="false" >
279-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
280            <meta-data
280-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
281                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
281-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
282                android:value="androidx.startup" />
282-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\992546156124c30573e74e4b3e96838f\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
283            <meta-data
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
284                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
285                android:value="androidx.startup" />
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
286        </provider>
287
288        <uses-library
288-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08ad2e43d7092c689ba72edfa0286f7\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
289            android:name="android.ext.adservices"
289-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08ad2e43d7092c689ba72edfa0286f7\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
290            android:required="false" />
290-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a08ad2e43d7092c689ba72edfa0286f7\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
291
292        <meta-data
292-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e829c71dd9346aa9726de2f25cdf8480\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
293            android:name="com.google.android.gms.version"
293-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e829c71dd9346aa9726de2f25cdf8480\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
294            android:value="@integer/google_play_services_version" />
294-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e829c71dd9346aa9726de2f25cdf8480\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
295
296        <service
296-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6315db21683a374ca91d9d960c0e30d2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
297            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
297-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6315db21683a374ca91d9d960c0e30d2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
298            android:exported="false" >
298-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6315db21683a374ca91d9d960c0e30d2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
299            <meta-data
299-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6315db21683a374ca91d9d960c0e30d2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
300                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
300-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6315db21683a374ca91d9d960c0e30d2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
301                android:value="cct" />
301-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6315db21683a374ca91d9d960c0e30d2\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
302        </service>
303        <service
303-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
304            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
304-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
305            android:exported="false"
305-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
306            android:permission="android.permission.BIND_JOB_SERVICE" >
306-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
307        </service>
308
309        <receiver
309-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
310            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
310-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
311            android:exported="false" />
311-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0537aedc1146e6c6d3e2410f94fefa8f\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
312        <receiver
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
313            android:name="androidx.profileinstaller.ProfileInstallReceiver"
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
314            android:directBootAware="false"
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
315            android:enabled="true"
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
316            android:exported="true"
316-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
317            android:permission="android.permission.DUMP" >
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
318            <intent-filter>
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
319                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
319-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
320            </intent-filter>
321            <intent-filter>
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
322                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
322-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
323            </intent-filter>
324            <intent-filter>
324-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
325                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
325-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
326            </intent-filter>
327            <intent-filter>
327-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
328                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
328-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abaf19e948f5b086c60987f617843bec\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
329            </intent-filter>
330        </receiver>
331    </application>
332
333</manifest>
