import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/routing/app_routes.dart';
import '../../../core/storage/web_storage_service.dart';
import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_stepper.dart';
import '../widgets/wizard_page.dart';
import '../steps/business_profile_step.dart';
import '../steps/location_setup_step.dart';
import '../steps/service_creation_step.dart';
import '../steps/queue_management_step.dart';
import '../steps/summary_step.dart';
import 'onboarding_completion_screen.dart';

/// Main onboarding wizard screen
class OnboardingWizardScreen extends ConsumerStatefulWidget {
  const OnboardingWizardScreen({super.key});

  @override
  ConsumerState<OnboardingWizardScreen> createState() =>
      _OnboardingWizardScreenState();
}

class _OnboardingWizardScreenState
    extends ConsumerState<OnboardingWizardScreen> {
  late OnboardingWizardController _wizardController;

  // Define the steps for the wizard
  static const List<OnboardingStep> _wizardSteps = [
    OnboardingStep.businessProfile,
    OnboardingStep.locationSetup,
    OnboardingStep.serviceCreation,
    OnboardingStep.queueManagement,
    OnboardingStep.summary,
  ];

  @override
  void initState() {
    super.initState();

    // Initialize wizard controller
    _wizardController = OnboardingWizardController(steps: _wizardSteps);

    // Load existing progress
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingProgress();
    });
  }

  @override
  void dispose() {
    _wizardController.dispose();
    super.dispose();
  }

  /// Load existing onboarding progress
  void _loadExistingProgress() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final data = onboardingState.data;

    if (data != null) {
      // Set wizard to current step
      _wizardController.goToStep(data.currentStep, animate: false);

      // Update completion state for each step
      for (final step in _wizardSteps) {
        _wizardController.markStepCompleted(step, data.isStepCompleted(step));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup Your Business'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _showExitConfirmation,
        ),
        actions: [
          // Progress indicator in app bar
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Center(
              child: Text(
                '${_wizardController.currentIndex + 1}/${_wizardController.totalSteps}',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.colors.onSurfaceVariant,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Wizard stepper
          Container(
            color: context.colors.surface,
            child: Column(
              children: [
                // Step indicators
                AnimatedBuilder(
                  animation: _wizardController,
                  builder: (context, child) {
                    return WizardStepper(
                      currentStep: _wizardController.currentStep,
                      steps: _wizardSteps,
                      onStepTapped: _onStepTapped,
                    );
                  },
                ),

                // Progress bar
                AnimatedBuilder(
                  animation: _wizardController,
                  builder: (context, child) {
                    return WizardProgressIndicator(
                      progress: _wizardController.progress,
                      showPercentage: false,
                    );
                  },
                ),

                const SizedBox(height: 8),
              ],
            ),
          ),

          // Main content area
          Expanded(
            child: AnimatedBuilder(
              animation: _wizardController,
              builder: (context, child) {
                if (onboardingState.isLoading) {
                  return const WizardLoadingState(
                    message: 'Loading your progress...',
                  );
                }

                if (onboardingState.error != null) {
                  return WizardErrorState(
                    message: onboardingState.error!,
                    onRetry: () => ref
                        .read(onboardingNotifierProvider.notifier)
                        .startOnboarding(),
                  );
                }

                return PageView(
                  controller: _wizardController.pageController,
                  onPageChanged: _wizardController.onPageChanged,
                  children: _buildWizardPages(),
                );
              },
            ),
          ),

          // Navigation buttons
          AnimatedBuilder(
            animation: _wizardController,
            builder: (context, child) {
              return WizardNavigationButtons(
                canGoBack: _wizardController.canGoBack,
                canProceed: _wizardController.canProceed,
                isLoading: onboardingState.isSaving,
                nextButtonText: _getNextButtonText(),
                onNext: _onNext,
                onBack: _onBack,
              );
            },
          ),
        ],
      ),
    );
  }

  /// Build wizard pages
  List<Widget> _buildWizardPages() {
    return [
      BusinessProfileStep(controller: _wizardController),
      LocationSetupStep(controller: _wizardController),
      ServiceCreationStep(controller: _wizardController),
      QueueManagementStep(controller: _wizardController),
      SummaryStep(controller: _wizardController),
    ];
  }

  /// Handle step tapped in stepper
  void _onStepTapped(OnboardingStep step) {
    if (_wizardController.isStepAccessible(step)) {
      _wizardController.goToStep(step);
    } else {
      _showStepNotAccessibleMessage(step);
    }
  }

  /// Handle next button pressed
  void _onNext() async {
    final currentStep = _wizardController.currentStep;

    // Validate current step before proceeding
    if (await _validateCurrentStep()) {
      if (currentStep == OnboardingStep.summary) {
        // Complete onboarding
        await _completeOnboarding();
      } else {
        // Move to next step
        await _wizardController.nextStep();
      }
    }
  }

  /// Handle back button pressed
  void _onBack() {
    _wizardController.previousStep();
  }

  /// Validate current step
  Future<bool> _validateCurrentStep() async {
    // This will be implemented by each step widget
    // For now, return true
    return true;
  }

  /// Complete onboarding process
  Future<void> _completeOnboarding() async {
    try {
      final completion = await ref
          .read(onboardingNotifierProvider.notifier)
          .completeOnboarding();

      if (completion != null && mounted) {
        // Navigate to completion screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                OnboardingCompletionScreen(completion: completion),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('Failed to complete onboarding: $e');
      }
    }
  }

  /// Get next button text based on current step
  String _getNextButtonText() {
    switch (_wizardController.currentStep) {
      case OnboardingStep.summary:
        return 'Complete Setup';
      case OnboardingStep.completed:
        return 'Get Started';
      default:
        return 'Next';
    }
  }

  /// Show step not accessible message
  void _showStepNotAccessibleMessage(OnboardingStep step) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Please complete the previous steps to access ${step.title}',
        ),
        backgroundColor: context.colors.error,
      ),
    );
  }

  /// Show exit confirmation dialog with skip option
  void _showExitConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Skip Business Setup?'),
        content: const Text(
          'You can skip the setup for now and configure your business later. Are you sure you want to skip?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // Save skip flag to local storage
              await WebStorageService.setOnboardingSkipped(true);
              // Navigate to dashboard
              if (mounted) {
                context.go(AppRoutes.dashboard);
              }
            },
            child: const Text('Skip Setup'),
          ),
        ],
      ),
    );
  }

  /// Show completion success message
  void _showCompletionSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text('Business setup completed successfully!'),
          ],
        ),
        backgroundColor: context.colors.primary,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Show error message
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: context.colors.error),
    );
  }
}
