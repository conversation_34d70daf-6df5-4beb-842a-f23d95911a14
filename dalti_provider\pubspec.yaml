name: dalti_provider
description: "Dalti Provider Mobile App - Comprehensive business management for service providers"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Localization
  flutter_localizations:
    sdk: flutter

  # Firebase
  firebase_core: ^3.15.0
  firebase_messaging: ^15.1.4
  flutter_local_notifications: ^17.2.1

  # State Management
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # HTTP Client
  dio: ^5.4.0

  # Navigation
  go_router: ^14.0.0

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # JSON Serialization
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.1

  # Secure Storage
  flutter_secure_storage: ^9.0.0

  # UI Components
  cupertino_icons: ^1.0.8

  # Utilities
  equatable: ^2.0.5

  # Location Services
  geolocator: ^12.0.0

  # URL Launcher
  url_launcher: ^6.3.1

  # Calendar
  table_calendar: ^3.1.0

  # Internationalization
  intl: ^0.20.2

  # File Picker (cross-platform)
  file_picker: ^8.0.7

  # HTTP for multipart requests
  http: ^1.1.0

  # MIME type detection
  mime: ^1.0.4

  # Path utilities
  path: ^1.8.3

  # Phone number validation
  phone_numbers_parser: ^8.3.0

  # QR Code Scanner (mobile_scanner is more modern and web-compatible)
  mobile_scanner: ^5.0.0

  # WebSocket/Socket.IO for real-time communication
  web_socket_channel: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0

  # Code Generation
  riverpod_generator: ^2.4.0
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  freezed: ^2.5.2

  # Testing
  mocktail: ^1.0.0

  # Linting
  custom_lint: ^0.6.4
  riverpod_lint: ^2.3.10

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Internationalization configuration
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
