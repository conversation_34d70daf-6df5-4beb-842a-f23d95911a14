import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../theme/theme_provider.dart';
import '../localization/widgets/language_selector.dart';
import '../../features/auth/providers/auth_provider.dart';
import '../../generated/l10n/app_localizations.dart';
import 'rive_menu_section.dart';
import 'rive_models/rive_menu_item.dart';

/// Animated sidebar widget with Rive animations
class AnimatedSidebar extends ConsumerStatefulWidget {
  final bool isOpen;
  final VoidCallback onClose;
  final String currentPath;

  const AnimatedSidebar({
    super.key,
    required this.isOpen,
    required this.onClose,
    required this.currentPath,
  });

  @override
  ConsumerState<AnimatedSidebar> createState() => _AnimatedSidebarState();
}

class _AnimatedSidebarState extends ConsumerState<AnimatedSidebar>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    if (widget.isOpen) {
      _slideController.forward();
      _fadeController.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedSidebar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isOpen != oldWidget.isOpen) {
      if (widget.isOpen) {
        _slideController.forward();
        _fadeController.forward();
      } else {
        _slideController.reverse();
        _fadeController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isOpen && _slideController.isDismissed) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        // Backdrop
        AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return GestureDetector(
              onTap: widget.onClose,
              child: Container(
                color: Colors.black.withValues(alpha: _fadeAnimation.value),
                width: double.infinity,
                height: double.infinity,
              ),
            );
          },
        ),

        // Sidebar
        AnimatedBuilder(
          animation: _slideAnimation,
          builder: (context, child) {
            return SlideTransition(
              position: _slideAnimation,
              child: _buildSidebar(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: 280,
      height: double.infinity,
      decoration: BoxDecoration(
        color: context.isDarkMode ? const Color(0xFF1E2139) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(
              alpha: context.isDarkMode ? 0.3 : 0.1,
            ),
            blurRadius: 20,
            offset: const Offset(4, 0),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildMenuItems()),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),

      child: Row(
        children: [
          // Logo
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  context.colors.primary,
                  context.colors.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: context.colors.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Image.asset(
              'assets/images/logo-dark.png',
              width: 32,
              height: 32,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.business,
                  size: 24,
                  color: Colors.white,
                );
              },
            ),
          ),
          const SizedBox(width: 16),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Dalti',
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: context.isDarkMode ? Colors.white : Colors.black87,
                    fontSize: 20,
                  ),
                ),
                Text(
                  'Provider Dashboard',
                  style: context.textTheme.bodySmall?.copyWith(
                    color:
                        context.isDarkMode
                            ? Colors.white.withValues(alpha: 0.6)
                            : Colors.black54,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          Container(
            decoration: BoxDecoration(
              color:
                  context.isDarkMode
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              onPressed: widget.onClose,
              icon: Icon(
                Icons.close,
                size: 20,
                color: context.isDarkMode ? Colors.white70 : Colors.black54,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItems() {
    return SingleChildScrollView(
      child: Column(
        children: [
          RiveMenuSection(
            title: 'GENERAL',
            selectedRoute: widget.currentPath,
            menuItems: RiveMenuItemModel.generalMenuItems,
            onMenuPress: (menu) => _navigateToRoute(menu.route),
          ),

          RiveMenuSection(
            title: 'MANAGEMENT',
            selectedRoute: widget.currentPath,
            menuItems: RiveMenuItemModel.managementMenuItems,
            onMenuPress: (menu) => _navigateToRoute(menu.route),
          ),

          RiveMenuSection(
            title: 'SETTINGS',
            selectedRoute: widget.currentPath,
            menuItems: RiveMenuItemModel.settingsMenuItems,
            onMenuPress: (menu) => _navigateToRoute(menu.route),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    final l10n = AppLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            context.isDarkMode
                ? const Color(0xFF252B42)
                : context.colors.primary.withValues(alpha: 0.02),
        border: Border(
          top: BorderSide(
            color:
                context.isDarkMode
                    ? Colors.white.withValues(alpha: 0.1)
                    : Colors.black.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 8),

          // Theme toggle
          RiveMenuSection(
            title: '',
            selectedRoute: '',
            menuItems: [
              RiveMenuItemModel(
                title: 'Toggle Theme',
                route: '',
                riveIcon: RiveMenuItemModel.themeMenuItem[0].riveIcon,
              ),
            ],
            onMenuPress: (menu) {
              ref.read(themeNotifierProvider.notifier).toggleTheme();
            },
          ),

          // Language selector
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: LanguageSettingsTile(),
          ),

          const SizedBox(height: 16),

          // Logout
          RiveMenuSection(
            title: '',
            selectedRoute: '',
            menuItems: [
              RiveMenuItemModel(
                title: l10n.logout,
                route: '',
                riveIcon: RiveMenuItemModel.themeMenuItem[0].riveIcon,
              ),
            ],
            onMenuPress: (menu) => _showLogoutDialog(),
          ),
        ],
      ),
    );
  }

  void _navigateToRoute(String route) {
    if (route.isEmpty) {
      // Handle special actions like theme toggle
      ref.read(themeNotifierProvider.notifier).toggleTheme();
      return;
    }

    widget.onClose();
    context.go(route);
  }

  void _showLogoutDialog() {
    widget.onClose();
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.logoutConfirmTitle),
            content: Text(l10n.logoutConfirmMessage),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(l10n.cancel),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(authNotifierProvider.notifier).logout();
                  context.go('/login');
                },
                style: TextButton.styleFrom(
                  foregroundColor: context.colors.error,
                ),
                child: Text(l10n.logout),
              ),
            ],
          ),
    );
  }
}
