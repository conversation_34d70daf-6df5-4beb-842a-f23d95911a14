import 'package:flutter/material.dart';
import '../theme/theme_provider.dart';

/// A widget to preview the current theme colors
/// Useful for testing dark theme implementation
class ThemePreview extends StatelessWidget {
  const ThemePreview({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Preview'),
        backgroundColor: Colors.transparent,
        actions: [
          IconButton(
            icon: Icon(context.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              // Toggle theme (if theme provider is available)
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Color Palette Section
            _buildSection(context, 'Color Palette', [
              _buildColorTile(context, 'Primary', context.colors.primary),
              _buildColorTile(context, 'Secondary', context.colors.secondary),
              _buildColorTile(context, 'Tertiary', context.colors.tertiary),
              _buildColorTile(context, 'Surface', context.colors.surface),
              _buildColorTile(context, 'Background', context.colors.background),
              _buildColorTile(
                context,
                'Surface Variant',
                context.colors.surfaceVariant,
              ),
            ]),

            const SizedBox(height: 24),

            // Text Colors Section
            _buildSection(context, 'Text Colors', [
              _buildTextSample(
                context,
                'Primary Text',
                context.colors.onSurface,
              ),
              _buildTextSample(
                context,
                'Secondary Text',
                context.colors.onSurfaceVariant,
              ),
              _buildTextSample(context, 'On Primary', context.colors.onPrimary),
            ]),

            const SizedBox(height: 24),

            // Component Preview Section
            _buildSection(context, 'Components', [
              // Cards
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sample Card',
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'This is a sample card to preview the dark theme colors.',
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.colors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {},
                      child: const Text('Primary Button'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {},
                      child: const Text('Outlined Button'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Progress Indicator
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Progress Indicators',
                    style: context.textTheme.titleSmall,
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: 0.7,
                    backgroundColor: context.colors.surfaceVariant,
                  ),
                  const SizedBox(height: 8),
                  const CircularProgressIndicator(),
                ],
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildColorTile(BuildContext context, String name, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: context.colors.outline, width: 1),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: context.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '#${color.value.toRadixString(16).toUpperCase().substring(2)}',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextSample(BuildContext context, String name, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              name,
              style: context.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'Sample text in $name color',
              style: context.textTheme.bodyMedium?.copyWith(color: color),
            ),
          ),
        ],
      ),
    );
  }
}
