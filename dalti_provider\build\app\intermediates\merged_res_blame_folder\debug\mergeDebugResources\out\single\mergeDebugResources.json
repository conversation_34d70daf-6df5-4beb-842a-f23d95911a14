[{"merged": "com.example.dalti_provider.app-debug-55:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.dalti_provider.app-main-49:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.dalti_provider.app-debug-55:/drawable-v21_launch_background.xml.flat", "source": "com.example.dalti_provider.app-main-49:/drawable-v21/launch_background.xml"}, {"merged": "com.example.dalti_provider.app-debug-55:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.dalti_provider.app-main-49:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.dalti_provider.app-debug-55:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.dalti_provider.app-main-49:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.dalti_provider.app-debug-55:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.dalti_provider.app-main-49:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.dalti_provider.app-debug-55:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.dalti_provider.app-main-49:/mipmap-hdpi/ic_launcher.png"}]